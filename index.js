import jsonfile from "jsonfile";
import moment from "moment";
import simpleGit from "simple-git";
import random from "random";

const path = "./data.json";
const git  = simpleGit();

// ─── CONFIG ─────────────────────────────────────────────────────
const COMMITS_IN_2024 = 250;   // reduced from 530 to 250
const COMMITS_IN_2025 = 50;    // reduced from 260 to 50

// gap months for certain periods (0-based: 2=Mar,3=Apr,4=May)
const isInGap = (m) => m >= 2 && m <= 4;

// define date ranges
const START_2024 = moment("2024-01-01").startOf("day");
const END_2024 = moment("2024-12-31").endOf("day");
const DAYS_2024 = END_2024.diff(START_2024, "days");

const START_2025 = moment("2025-01-01").startOf("day");
const END_2025 = moment().endOf("day");
const DAYS_2025 = END_2025.diff(START_2025, "days");
// ────────────────────────────────────────────────────────────────

/** pick a random date in 2024, retry if in gap months */
function pick2024Date() {
  while (true) {
    const d = START_2024.clone().add(random.int(0, DAYS_2024), "days");
    if (!isInGap(d.month())) return d;
  }
}

/** pick a random date in 2025 */
function pick2025Date() {
  return START_2025.clone().add(random.int(0, DAYS_2025), "days");
}

async function runAll() {
  // 1) collect dates
  const allDates = [];

  // Add commits for 2024 (250 commits)
  for (let i = 0; i < COMMITS_IN_2024; i++) {
    allDates.push(pick2024Date());
  }

  // Add commits for 2025 (50 commits)
  for (let i = 0; i < COMMITS_IN_2025; i++) {
    allDates.push(pick2025Date());
  }

  // 2) sort chronologically
  allDates.sort((a, b) => a.valueOf() - b.valueOf());

  // 3) commit each one
  for (const when of allDates) {
    const dateStr = when.format();
    console.log("Committing on:", dateStr);

    jsonfile.writeFileSync(path, { date: dateStr });
    await git.add([path]);
    await git.commit(`chore: commit on ${dateStr}`, { "--date": dateStr });
  }

  // 4) final push
  await git.push();
  console.log("✅ Done: pushed", allDates.length, "commits.");
  console.log(`Generated ${COMMITS_IN_2024} commits for 2024 and ${COMMITS_IN_2025} commits for 2025`);
}

runAll().catch(console.error);
