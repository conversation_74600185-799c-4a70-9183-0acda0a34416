import jsonfile from "jsonfile";
import moment from "moment";
import simpleGit from "simple-git";
import random from "random";

const path = "./data.json";
const git  = simpleGit();

// ─── CONFIG ─────────────────────────────────────────────────────
const COMMITS_IN_2023     = 15;   // how many in 2023
const COMMITS_IN_RECENT  = 90;   // how many in last 18 months

// gap months for the recent window (0-based: 2=Mar,3=Apr,4=May)
const isInGap = (m) => m >= 2 && m <= 4;

// define your 18-month window
const START_RECENT = moment()
  .subtract(1, "year")
  .subtract(6, "months")
  .startOf("day");
const END_RECENT   = moment().endOf("day");
const DAYS_RECENT  = END_RECENT.diff(START_RECENT, "days");
// ────────────────────────────────────────────────────────────────

/** pick a random date in the recent window, retry if in Mar–May */
function pickRecentDate() {
  while (true) {
    const d = START_RECENT.clone().add(random.int(0, DAYS_RECENT), "days");
    if (!isInGap(d.month())) return d;
  }
}

/** pick a random day anywhere in 2023 */
function pick2023Date() {
  return moment("2023-01-01")
    .add(random.int(0, 364), "days");
}

async function runAll() {
  // 1) collect dates
  const allDates = [];

  for (let i = 0; i < COMMITS_IN_2023; i++) {
    allDates.push(pick2023Date());
  }
  for (let i = 0; i < COMMITS_IN_RECENT; i++) {
    allDates.push(pickRecentDate());
  }

  // 2) optional: sort chronologically
  allDates.sort((a, b) => a.valueOf() - b.valueOf());

  // 3) commit each one
  for (const when of allDates) {
    const dateStr = when.format();
    console.log("Committing on:", dateStr);

    jsonfile.writeFileSync(path, { date: dateStr });
    await git.add([path]);
    await git.commit(`chore: commit on ${dateStr}`, { "--date": dateStr });
  }

  // 4) final push
  await git.push();
  console.log("✅ Done: pushed", allDates.length, "commits.");
}

runAll().catch(console.error);
