import jsonfile from "jsonfile";
import moment from "moment";
import simpleGit from "simple-git";
import random from "random";

const path = "./data.json";
const git = simpleGit();

// ─── CONFIG ─────────────────────────────────────────────────────
const COMMITS_IN_2024 = 250;   
const COMMITS_IN_2025 = 50;    

// define date ranges
const START_2024 = moment("2024-01-01").startOf("day");
const END_2024 = moment("2024-12-31").endOf("day");
const DAYS_2024 = END_2024.diff(START_2024, "days");

const START_2025 = moment("2025-01-01").startOf("day");
const END_2025 = moment().endOf("day");
const DAYS_2025 = END_2025.diff(START_2025, "days");

function pick2024Date() {
  return START_2024.clone().add(random.int(0, DAYS_2024), "days");
}

function pick2025Date() {
  return START_2025.clone().add(random.int(0, DAYS_2025), "days");
}

async function runAll() {
  const allDates = [];

  for (let i = 0; i < COMMITS_IN_2024; i++) {
    allDates.push(pick2024Date());
  }
  
  for (let i = 0; i < COMMITS_IN_2025; i++) {
    allDates.push(pick2025Date());
  }

  allDates.sort((a, b) => a.valueOf() - b.valueOf());

  for (const when of allDates) {
    const dateStr = when.format();
    console.log("Committing on:", dateStr);

    jsonfile.writeFileSync(path, { date: dateStr });
    await git.add([path]);
    await git.commit(`chore: commit on ${dateStr}`, { "--date": dateStr });
  }

  await git.push();
  console.log("✅ Done: pushed", allDates.length, "commits.");
  console.log(`Generated ${COMMITS_IN_2024} commits for 2024 and ${COMMITS_IN_2025} commits for 2025`);
}

runAll().catch(console.error);
